import 'package:flutter/foundation.dart';
import 'dart:async';
import '../services/network_service.dart';
import 'game_state.dart';
import 'piece.dart';

// 联机游戏状态管理器
class NetworkGameState extends GameState {
  final NetworkService _networkService = NetworkService();
  
  // 网络相关状态
  bool _isOnlineMode = false;
  bool _isHost = false;
  String? _roomId;
  String? _opponentId;
  PlayerType? _localPlayerType;
  
  // 连接状态
  bool _isConnected = false;
  String _connectionStatus = '未连接';
  
  StreamSubscription<NetworkMessage>? _messageSubscription;
  StreamSubscription<bool>? _connectionSubscription;

  // Getters
  bool get isOnlineMode => _isOnlineMode;
  bool get isHost => _isHost;
  bool get isConnected => _isConnected;
  String get connectionStatus => _connectionStatus;
  String? get roomId => _roomId;
  PlayerType? get localPlayerType => _localPlayerType;
  bool get isLocalPlayerTurn => _localPlayerType == currentPlayer;

  @override
  void initializeGame() {
    super.initializeGame();
    if (_isOnlineMode) {
      _setupNetworkListeners();
    }
  }

  // 初始化网络服务
  Future<void> initializeNetworkService() async {
    try {
      await _networkService.initialize();
      _setupNetworkListeners();
    } catch (e) {
      print('网络服务初始化失败: $e');
      _connectionStatus = '连接失败';
      notifyListeners();
    }
  }

  // 设置网络监听器
  void _setupNetworkListeners() {
    // 监听网络消息
    _messageSubscription?.cancel();
    _messageSubscription = _networkService.messageStream.listen(_handleNetworkMessage);
    
    // 监听连接状态
    _connectionSubscription?.cancel();
    _connectionSubscription = _networkService.connectionStream.listen(_handleConnectionChange);
  }

  // 处理网络消息
  void _handleNetworkMessage(NetworkMessage message) {
    switch (message.type) {
      case MessageType.roomCreated:
        _handleRoomCreated(message);
        break;
      case MessageType.playerJoined:
        _handlePlayerJoined(message);
        break;
      case MessageType.playerLeft:
        _handlePlayerLeft(message);
        break;
      case MessageType.playerReady:
        _handlePlayerReady(message);
        break;
      case MessageType.diceRolled:
        _handleDiceRolled(message);
        break;
      case MessageType.pieceMove:
        _handlePieceMove(message);
        break;
      case MessageType.error:
        _handleError(message);
        break;
      default:
        print('未处理的消息类型: ${message.type}');
    }
  }

  // 处理连接状态变化
  void _handleConnectionChange(bool connected) {
    _isConnected = connected;
    _connectionStatus = connected ? '已连接' : '连接断开';
    notifyListeners();
  }

  // 处理房间创建成功
  void _handleRoomCreated(NetworkMessage message) {
    _roomId = message.data['roomId'];
    _isHost = true;
    _localPlayerType = PlayerType.player1;
    _isOnlineMode = true;
    _connectionStatus = '房间创建成功，等待玩家加入';
    notifyListeners();
  }

  // 处理玩家加入
  void _handlePlayerJoined(NetworkMessage message) {
    if (message.data.containsKey('playerType')) {
      // 自己加入房间的响应
      _localPlayerType = message.data['playerType'] == 'player1' 
          ? PlayerType.player1 
          : PlayerType.player2;
      _isOnlineMode = true;
      _roomId = message.roomId;
      _connectionStatus = '已加入房间';
    } else {
      // 其他玩家加入的通知
      _opponentId = message.data['newPlayer'];
      _connectionStatus = '对手已加入，可以开始游戏';
    }
    notifyListeners();
  }

  // 处理玩家离开
  void _handlePlayerLeft(NetworkMessage message) {
    _opponentId = null;
    _connectionStatus = '对手已离开';
    // 重置游戏状态到准备阶段
    currentPhase = GamePhase.preparation;
    player1Ready = false;
    player2Ready = false;
    notifyListeners();
  }

  // 处理玩家就绪
  void _handlePlayerReady(NetworkMessage message) {
    final playerType = message.data['playerType'];
    if (playerType == 'player1') {
      player1Ready = true;
    } else if (playerType == 'player2') {
      player2Ready = true;
    }
    
    // 如果双方都就绪，开始摇骰子
    if (player1Ready && player2Ready && _isHost) {
      // 只有房主负责摇骰子，避免重复
      Future.delayed(Duration(milliseconds: 500), () {
        _rollBothDiceNetwork();
      });
    }
    
    notifyListeners();
  }

  // 处理骰子结果
  void _handleDiceRolled(NetworkMessage message) {
    player1DiceValue = message.data['player1Dice'];
    player2DiceValue = message.data['player2Dice'];
    player1HasRolled = true;
    player2HasRolled = true;
    
    // 确定移动权限
    _determineMoveRights();
    currentPhase = GamePhase.moving;
    notifyListeners();
  }

  // 处理棋子移动
  void _handlePieceMove(NetworkMessage message) {
    final data = message.data;
    final pieceId = data['pieceId'];
    final toRow = data['toRow'];
    final toCol = data['toCol'];
    
    // 找到对应的棋子并移动
    GamePiece? piece = _findPieceById(pieceId);
    if (piece != null) {
      // 执行移动（不通过网络发送，因为这是接收到的移动）
      _executePieceMoveLocally(piece, toRow, toCol);
    }
  }

  // 处理错误
  void _handleError(NetworkMessage message) {
    _connectionStatus = '错误: ${message.data['error']}';
    notifyListeners();
  }

  // 创建房间
  Future<void> createRoom(String roomName) async {
    try {
      await _networkService.createRoom(roomName);
      _connectionStatus = '正在创建房间...';
      notifyListeners();
    } catch (e) {
      _connectionStatus = '创建房间失败';
      notifyListeners();
    }
  }

  // 加入房间
  Future<void> joinRoom(String roomId) async {
    try {
      await _networkService.joinRoom(roomId);
      _connectionStatus = '正在加入房间...';
      notifyListeners();
    } catch (e) {
      _connectionStatus = '加入房间失败';
      notifyListeners();
    }
  }

  // 离开房间
  Future<void> leaveRoom() async {
    await _networkService.leaveRoom();
    _isOnlineMode = false;
    _isHost = false;
    _roomId = null;
    _opponentId = null;
    _localPlayerType = null;
    _connectionStatus = '已离开房间';
    initializeGame(); // 重置游戏状态
  }

  // 网络版玩家就绪
  @override
  void setPlayerReady(PlayerType player) {
    // 只允许本地玩家设置就绪状态
    if (_isOnlineMode && player != _localPlayerType) return;
    
    super.setPlayerReady(player);
    
    // 发送就绪状态到网络
    if (_isOnlineMode) {
      _networkService.sendPlayerReady(player);
    }
  }

  // 网络版摇骰子
  void _rollBothDiceNetwork() {
    if (_isHost) {
      // 只有房主摇骰子
      super._rollBothDice();
      // 发送骰子结果到网络
      _networkService.sendDiceResult(player1DiceValue, player2DiceValue);
    }
  }

  // 网络版棋子移动
  @override
  void movePiece(int newRow, int newCol) {
    if (_isOnlineMode) {
      // 检查是否是本地玩家的回合
      if (selectedPiece?.player != _localPlayerType) return;
      if (!playersCanMove.contains(_localPlayerType)) return;
    }

    if (selectedPiece == null || !canMoveTo(newRow, newCol)) return;

    final piece = selectedPiece!;
    final fromRow = piece.row;
    final fromCol = piece.col;

    // 发送移动到网络（如果是联机模式）
    if (_isOnlineMode) {
      _networkService.sendPieceMove(piece.id, fromRow, fromCol, newRow, newCol);
    }

    // 执行本地移动
    _executePieceMoveLocally(piece, newRow, newCol);
  }

  // 本地执行棋子移动
  void _executePieceMoveLocally(GamePiece piece, int newRow, int newCol) {
    // 移除目标位置的敌方棋子
    GamePiece? capturedPiece = board[newRow][newCol];
    if (capturedPiece != null) {
      if (capturedPiece.player == PlayerType.player1) {
        player1Pieces.remove(capturedPiece);
      } else {
        player2Pieces.remove(capturedPiece);
      }
    }

    // 移动棋子
    board[piece.row][piece.col] = null;
    piece.row = newRow;
    piece.col = newCol;
    piece.isSelected = false;
    board[newRow][newCol] = piece;

    PlayerType movedPlayer = piece.player;
    selectedPiece = null;
    
    // 从可移动玩家列表中移除已移动的玩家
    playersCanMove.remove(movedPlayer);
    
    // 如果是平局情况且还有玩家可以移动，切换到下一个玩家
    if (playersCanMove.isNotEmpty) {
      currentPlayer = playersCanMove.first;
    } else {
      // 所有可移动的玩家都已移动，结束当前回合
      _endCurrentRound();
    }
    
    notifyListeners();
  }

  // 根据ID查找棋子
  GamePiece? _findPieceById(String pieceId) {
    for (final piece in player1Pieces) {
      if (piece.id == pieceId) return piece;
    }
    for (final piece in player2Pieces) {
      if (piece.id == pieceId) return piece;
    }
    return null;
  }

  // 切换到离线模式
  void switchToOfflineMode() {
    _isOnlineMode = false;
    _isHost = false;
    _roomId = null;
    _opponentId = null;
    _localPlayerType = null;
    _connectionStatus = '离线模式';
    initializeGame();
  }

  @override
  void dispose() {
    _messageSubscription?.cancel();
    _connectionSubscription?.cancel();
    _networkService.dispose();
    super.dispose();
  }
}
