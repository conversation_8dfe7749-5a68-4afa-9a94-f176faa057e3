import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/network_game_state.dart';
import 'game_screen.dart';
import 'network_game_screen.dart';

class LobbyScreen extends StatefulWidget {
  const LobbyScreen({Key? key}) : super(key: key);

  @override
  State<LobbyScreen> createState() => _LobbyScreenState();
}

class _LobbyScreenState extends State<LobbyScreen> {
  final TextEditingController _roomNameController = TextEditingController();
  final TextEditingController _roomIdController = TextEditingController();
  bool _isLoading = false;

  @override
  void dispose() {
    _roomNameController.dispose();
    _roomIdController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('双人对战棋盘游戏'),
        backgroundColor: Colors.brown[300],
        elevation: 0,
      ),
      body: Consumer<NetworkGameState>(
        builder: (context, gameState, child) {
          return Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [Colors.brown[50]!, Colors.brown[100]!],
              ),
            ),
            child: Padding(
              padding: EdgeInsets.all(20),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // 游戏标题
                  Container(
                    padding: EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(15),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black26,
                          offset: Offset(0, 2),
                          blurRadius: 6,
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        Icon(
                          Icons.games,
                          size: 60,
                          color: Colors.brown[600],
                        ),
                        SizedBox(height: 10),
                        Text(
                          '双人对战棋盘游戏',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Colors.brown[800],
                          ),
                        ),
                        SizedBox(height: 5),
                        Text(
                          '选择游戏模式开始对战',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),

                  SizedBox(height: 30),

                  // 连接状态显示
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: gameState.isConnected
                          ? Colors.green[100]
                          : Colors.red[100],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: gameState.isConnected
                            ? Colors.green[300]!
                            : Colors.red[300]!,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          gameState.isConnected ? Icons.wifi : Icons.wifi_off,
                          color: gameState.isConnected
                              ? Colors.green[700]
                              : Colors.red[700],
                          size: 20,
                        ),
                        SizedBox(width: 8),
                        Text(
                          gameState.connectionStatus,
                          style: TextStyle(
                            color: gameState.isConnected
                                ? Colors.green[700]
                                : Colors.red[700],
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),

                  SizedBox(height: 30),

                  // 游戏模式选择
                  _buildGameModeCard(
                    context,
                    gameState,
                    '本地对战',
                    '在同一设备上进行双人对战',
                    Icons.people,
                    Colors.blue,
                    () => _startLocalGame(context),
                  ),

                  SizedBox(height: 15),

                  _buildGameModeCard(
                    context,
                    gameState,
                    '创建房间',
                    '创建联机房间等待朋友加入',
                    Icons.add_circle,
                    Colors.green,
                    gameState.isConnected
                        ? () => _showCreateRoomDialog(context, gameState)
                        : null,
                  ),

                  SizedBox(height: 15),

                  _buildGameModeCard(
                    context,
                    gameState,
                    '加入房间',
                    '输入房间ID加入朋友的游戏',
                    Icons.login,
                    Colors.orange,
                    gameState.isConnected
                        ? () => _showJoinRoomDialog(context, gameState)
                        : null,
                  ),

                  SizedBox(height: 30),

                  // 重新连接按钮
                  if (!gameState.isConnected)
                    ElevatedButton.icon(
                      onPressed:
                          _isLoading ? null : () => _reconnect(gameState),
                      icon: _isLoading
                          ? SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : Icon(Icons.refresh),
                      label: Text(_isLoading ? '连接中...' : '重新连接'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.brown[400],
                        padding:
                            EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                      ),
                    ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildGameModeCard(
    BuildContext context,
    NetworkGameState gameState,
    String title,
    String description,
    IconData icon,
    Color color,
    VoidCallback? onTap,
  ) {
    final bool enabled = onTap != null;

    return Card(
      elevation: enabled ? 4 : 1,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: EdgeInsets.all(20),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: enabled ? Colors.white : Colors.grey[100],
          ),
          child: Row(
            children: [
              Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: enabled ? color.withOpacity(0.1) : Colors.grey[300],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: enabled ? color : Colors.grey[500],
                  size: 30,
                ),
              ),
              SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: enabled ? Colors.black87 : Colors.grey[500],
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      description,
                      style: TextStyle(
                        fontSize: 14,
                        color: enabled ? Colors.grey[600] : Colors.grey[400],
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                color: enabled ? Colors.grey[400] : Colors.grey[300],
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _startLocalGame(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => GameScreen()),
    );
  }

  void _showCreateRoomDialog(BuildContext context, NetworkGameState gameState) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('创建房间'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: _roomNameController,
                decoration: InputDecoration(
                  labelText: '房间名称',
                  hintText: '输入房间名称',
                  border: OutlineInputBorder(),
                ),
                maxLength: 20,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _roomNameController.clear();
              },
              child: Text('取消'),
            ),
            ElevatedButton(
              onPressed: () {
                if (_roomNameController.text.trim().isNotEmpty) {
                  Navigator.of(context).pop();
                  _createRoom(gameState, _roomNameController.text.trim());
                  _roomNameController.clear();
                }
              },
              child: Text('创建'),
            ),
          ],
        );
      },
    );
  }

  void _showJoinRoomDialog(BuildContext context, NetworkGameState gameState) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('加入房间'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: _roomIdController,
                decoration: InputDecoration(
                  labelText: '房间ID',
                  hintText: '输入房间ID',
                  border: OutlineInputBorder(),
                ),
                maxLength: 36,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _roomIdController.clear();
              },
              child: Text('取消'),
            ),
            ElevatedButton(
              onPressed: () {
                if (_roomIdController.text.trim().isNotEmpty) {
                  Navigator.of(context).pop();
                  _joinRoom(gameState, _roomIdController.text.trim());
                  _roomIdController.clear();
                }
              },
              child: Text('加入'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _createRoom(NetworkGameState gameState, String roomName) async {
    setState(() => _isLoading = true);

    try {
      await gameState.createRoom(roomName);

      // 等待房间创建成功
      await Future.delayed(Duration(seconds: 1));

      if (gameState.roomId != null) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => NetworkGameScreen(isHost: true),
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('创建房间失败: $e')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _joinRoom(NetworkGameState gameState, String roomId) async {
    setState(() => _isLoading = true);

    try {
      await gameState.joinRoom(roomId);

      // 等待加入房间成功
      await Future.delayed(Duration(seconds: 1));

      if (gameState.roomId != null) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => NetworkGameScreen(isHost: false),
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('加入房间失败: $e')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _reconnect(NetworkGameState gameState) async {
    setState(() => _isLoading = true);

    try {
      await gameState.initializeNetworkService();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('重新连接失败: $e')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }
}
