import 'package:flutter/foundation.dart';
import 'piece.dart';

class GameState extends ChangeNotifier {
  static const int boardSize = 8;

  List<List<GamePiece?>> board = List.generate(
    boardSize,
    (index) => List.generate(boardSize, (index) => null),
  );

  PlayerType currentPlayer = PlayerType.player1;
  GamePiece? selectedPiece;
  int diceValue = 1;
  bool hasDiceRolled = false;
  bool hasMovedThisTurn = false;

  List<GamePiece> player1Pieces = [];
  List<GamePiece> player2Pieces = [];

  GameState() {
    initializeGame();
  }

  void initializeGame() {
    // 重置游戏状态
    board = List.generate(
      boardSize,
      (index) => List.generate(boardSize, (index) => null),
    );
    currentPlayer = PlayerType.player1;
    selectedPiece = null;
    diceValue = 1;
    hasDiceRolled = false;
    hasMovedThisTurn = false;
    player1Pieces.clear();
    player2Pieces.clear();

    // 初始化玩家1的棋子（红色，放在棋盘下方）
    for (int i = 0; i < 15; i++) {
      int row = 6 + (i ~/ 8);
      int col = i % 8;
      if (row < boardSize && col < boardSize) {
        final piece = GamePiece(
          id: 'p1_$i',
          player: PlayerType.player1,
          row: row,
          col: col,
        );
        player1Pieces.add(piece);
        board[row][col] = piece;
      }
    }

    // 初始化玩家2的棋子（蓝色，放在棋盘上方）
    for (int i = 0; i < 15; i++) {
      int row = 1 - (i ~/ 8);
      int col = i % 8;
      if (row >= 0 && col < boardSize) {
        final piece = GamePiece(
          id: 'p2_$i',
          player: PlayerType.player2,
          row: row,
          col: col,
        );
        player2Pieces.add(piece);
        board[row][col] = piece;
      }
    }
    notifyListeners();
  }

  void rollDice() {
    if (!hasDiceRolled) {
      diceValue = (DateTime.now().millisecondsSinceEpoch % 6) + 1;
      hasDiceRolled = true;
      notifyListeners();
    }
  }

  void selectPiece(GamePiece piece) {
    if (!hasDiceRolled || hasMovedThisTurn) return;
    if (piece.player != currentPlayer) return;

    // 取消之前选中的棋子
    if (selectedPiece != null) {
      selectedPiece!.isSelected = false;
    }

    selectedPiece = piece;
    piece.isSelected = true;
    notifyListeners();
  }

  bool canMoveTo(int newRow, int newCol) {
    if (selectedPiece == null || !hasDiceRolled) return false;

    // 检查边界
    if (newRow < 0 ||
        newRow >= boardSize ||
        newCol < 0 ||
        newCol >= boardSize) {
      return false;
    }

    // 检查移动距离是否等于骰子点数
    int distance =
        (selectedPiece!.row - newRow).abs() +
        (selectedPiece!.col - newCol).abs();
    if (distance != diceValue) return false;

    // 检查目标位置是否被己方棋子占据
    GamePiece? targetPiece = board[newRow][newCol];
    if (targetPiece != null && targetPiece.player == currentPlayer) {
      return false;
    }

    return true;
  }

  void movePiece(int newRow, int newCol) {
    if (selectedPiece == null || !canMoveTo(newRow, newCol)) return;

    // 移除目标位置的敌方棋子
    GamePiece? capturedPiece = board[newRow][newCol];
    if (capturedPiece != null) {
      if (capturedPiece.player == PlayerType.player1) {
        player1Pieces.remove(capturedPiece);
      } else {
        player2Pieces.remove(capturedPiece);
      }
    }

    // 移动棋子
    board[selectedPiece!.row][selectedPiece!.col] = null;
    selectedPiece!.row = newRow;
    selectedPiece!.col = newCol;
    selectedPiece!.isSelected = false;
    board[newRow][newCol] = selectedPiece;

    selectedPiece = null;
    hasMovedThisTurn = true;
    notifyListeners();
  }

  void endTurn() {
    if (!hasMovedThisTurn) return;

    currentPlayer =
        currentPlayer == PlayerType.player1
            ? PlayerType.player2
            : PlayerType.player1;
    hasDiceRolled = false;
    hasMovedThisTurn = false;
    selectedPiece = null;
    notifyListeners();
  }

  bool get isGameOver {
    return player1Pieces.isEmpty || player2Pieces.isEmpty;
  }

  PlayerType? get winner {
    if (player1Pieces.isEmpty) return PlayerType.player2;
    if (player2Pieces.isEmpty) return PlayerType.player1;
    return null;
  }
}
