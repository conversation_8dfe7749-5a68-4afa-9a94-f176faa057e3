import 'package:flutter/foundation.dart';
import 'dart:math';
import 'piece.dart';

// 游戏阶段枚举
enum GamePhase {
  preparation, // 准备阶段
  rolling, // 摇骰子阶段
  moving, // 移动阶段
}

class GameState extends ChangeNotifier {
  static const int boardSize = 8;

  List<List<GamePiece?>> board = List.generate(
    boardSize,
    (index) => List.generate(boardSize, (index) => null),
  );

  PlayerType currentPlayer = PlayerType.player1;
  GamePiece? selectedPiece;

  // 双骰子系统
  int player1DiceValue = 1;
  int player2DiceValue = 1;
  bool player1HasRolled = false;
  bool player2HasRolled = false;

  // 就绪状态
  bool player1Ready = false;
  bool player2Ready = false;

  // 游戏阶段
  GamePhase currentPhase = GamePhase.preparation;

  // 当前回合可以移动的玩家（基于骰子比较结果）
  Set<PlayerType> playersCanMove = {};

  List<GamePiece> player1Pieces = [];
  List<GamePiece> player2Pieces = [];

  Random _random = Random();

  GameState() {
    initializeGame();
  }

  void initializeGame() {
    // 重置游戏状态
    board = List.generate(
      boardSize,
      (index) => List.generate(boardSize, (index) => null),
    );
    currentPlayer = PlayerType.player1;
    selectedPiece = null;

    // 重置双骰子系统
    player1DiceValue = 1;
    player2DiceValue = 1;
    player1HasRolled = false;
    player2HasRolled = false;

    // 重置就绪状态
    player1Ready = false;
    player2Ready = false;

    // 重置游戏阶段
    currentPhase = GamePhase.preparation;

    // 重置移动状态
    playersCanMove.clear();

    player1Pieces.clear();
    player2Pieces.clear();

    // 初始化玩家1的棋子（红色，放在棋盘下方）
    for (int i = 0; i < 15; i++) {
      int row = 6 + (i ~/ 8);
      int col = i % 8;
      if (row < boardSize && col < boardSize) {
        final piece = GamePiece(
          id: 'p1_$i',
          player: PlayerType.player1,
          row: row,
          col: col,
        );
        player1Pieces.add(piece);
        board[row][col] = piece;
      }
    }

    // 初始化玩家2的棋子（蓝色，放在棋盘上方）
    for (int i = 0; i < 15; i++) {
      int row = 1 - (i ~/ 8);
      int col = i % 8;
      if (row >= 0 && col < boardSize) {
        final piece = GamePiece(
          id: 'p2_$i',
          player: PlayerType.player2,
          row: row,
          col: col,
        );
        player2Pieces.add(piece);
        board[row][col] = piece;
      }
    }
    notifyListeners();
  }

  // 玩家点击就绪按钮
  void setPlayerReady(PlayerType player) {
    if (currentPhase != GamePhase.preparation) return;

    if (player == PlayerType.player1) {
      player1Ready = true;
    } else {
      player2Ready = true;
    }

    // 如果双方都就绪，进入摇骰子阶段
    if (player1Ready && player2Ready) {
      currentPhase = GamePhase.rolling;
      _rollBothDice();
    }

    notifyListeners();
  }

  // 同时摇两个骰子
  void _rollBothDice() {
    player1DiceValue = _random.nextInt(6) + 1;
    player2DiceValue = _random.nextInt(6) + 1;
    player1HasRolled = true;
    player2HasRolled = true;

    // 确定谁可以移动
    _determineMoveRights();

    // 进入移动阶段
    currentPhase = GamePhase.moving;
    notifyListeners();
  }

  // 根据骰子结果确定移动权限
  void _determineMoveRights() {
    playersCanMove.clear();

    if (player1DiceValue > player2DiceValue) {
      // 玩家1获胜，只有玩家1可以移动
      playersCanMove.add(PlayerType.player1);
      currentPlayer = PlayerType.player1;
    } else if (player2DiceValue > player1DiceValue) {
      // 玩家2获胜，只有玩家2可以移动
      playersCanMove.add(PlayerType.player2);
      currentPlayer = PlayerType.player2;
    } else {
      // 平局，双方都可以移动
      playersCanMove.add(PlayerType.player1);
      playersCanMove.add(PlayerType.player2);
      currentPlayer = PlayerType.player1; // 从玩家1开始
    }
  }

  void selectPiece(GamePiece piece) {
    // 只有在移动阶段且该玩家有移动权限时才能选择棋子
    if (currentPhase != GamePhase.moving) return;
    if (!playersCanMove.contains(piece.player)) return;
    if (piece.player != currentPlayer) return;

    // 取消之前选中的棋子
    if (selectedPiece != null) {
      selectedPiece!.isSelected = false;
    }

    selectedPiece = piece;
    piece.isSelected = true;
    notifyListeners();
  }

  bool canMoveTo(int newRow, int newCol) {
    if (selectedPiece == null || currentPhase != GamePhase.moving) return false;
    if (!playersCanMove.contains(selectedPiece!.player)) return false;

    // 检查边界
    if (newRow < 0 ||
        newRow >= boardSize ||
        newCol < 0 ||
        newCol >= boardSize) {
      return false;
    }

    // 检查移动距离是否等于对应玩家的骰子点数
    int diceValue = selectedPiece!.player == PlayerType.player1
        ? player1DiceValue
        : player2DiceValue;
    int distance = (selectedPiece!.row - newRow).abs() +
        (selectedPiece!.col - newCol).abs();
    if (distance != diceValue) return false;

    // 检查目标位置是否被己方棋子占据
    GamePiece? targetPiece = board[newRow][newCol];
    if (targetPiece != null && targetPiece.player == selectedPiece!.player) {
      return false;
    }

    return true;
  }

  void movePiece(int newRow, int newCol) {
    if (selectedPiece == null || !canMoveTo(newRow, newCol)) return;

    // 移除目标位置的敌方棋子
    GamePiece? capturedPiece = board[newRow][newCol];
    if (capturedPiece != null) {
      if (capturedPiece.player == PlayerType.player1) {
        player1Pieces.remove(capturedPiece);
      } else {
        player2Pieces.remove(capturedPiece);
      }
    }

    // 移动棋子
    board[selectedPiece!.row][selectedPiece!.col] = null;
    selectedPiece!.row = newRow;
    selectedPiece!.col = newCol;
    selectedPiece!.isSelected = false;
    board[newRow][newCol] = selectedPiece;

    PlayerType movedPlayer = selectedPiece!.player;
    selectedPiece = null;

    // 从可移动玩家列表中移除已移动的玩家
    playersCanMove.remove(movedPlayer);

    // 如果是平局情况且还有玩家可以移动，切换到下一个玩家
    if (playersCanMove.isNotEmpty) {
      currentPlayer = playersCanMove.first;
    } else {
      // 所有可移动的玩家都已移动，结束当前回合
      _endCurrentRound();
    }

    notifyListeners();
  }

  // 结束当前回合，开始新的准备阶段
  void _endCurrentRound() {
    currentPhase = GamePhase.preparation;
    player1Ready = false;
    player2Ready = false;
    player1HasRolled = false;
    player2HasRolled = false;
    playersCanMove.clear();
    selectedPiece = null;
  }

  // 手动结束回合（如果玩家选择不移动）
  void skipMove() {
    if (currentPhase != GamePhase.moving) return;
    if (!playersCanMove.contains(currentPlayer)) return;

    // 从可移动玩家列表中移除当前玩家
    playersCanMove.remove(currentPlayer);

    // 如果还有其他玩家可以移动，切换到下一个玩家
    if (playersCanMove.isNotEmpty) {
      currentPlayer = playersCanMove.first;
    } else {
      // 所有可移动的玩家都已完成，结束当前回合
      _endCurrentRound();
    }

    notifyListeners();
  }

  // 获取骰子比较结果的描述
  String get diceComparisonResult {
    if (!player1HasRolled || !player2HasRolled) return '';

    if (player1DiceValue > player2DiceValue) {
      return '玩家1获胜！可以移动棋子';
    } else if (player2DiceValue > player1DiceValue) {
      return '玩家2获胜！可以移动棋子';
    } else {
      return '平局！双方都可以移动棋子';
    }
  }

  // 获取当前游戏阶段的描述
  String get phaseDescription {
    switch (currentPhase) {
      case GamePhase.preparation:
        return '准备阶段：等待玩家就绪';
      case GamePhase.rolling:
        return '摇骰子阶段';
      case GamePhase.moving:
        return '移动阶段';
    }
  }

  // 检查玩家是否可以移动
  bool canPlayerMove(PlayerType player) {
    return currentPhase == GamePhase.moving && playersCanMove.contains(player);
  }

  // 兼容性方法 - 为了保持与现有UI的兼容性
  bool get hasMovedThisTurn {
    return currentPhase == GamePhase.moving && playersCanMove.isEmpty;
  }

  // 兼容性方法 - 获取当前玩家的骰子值
  int get diceValue {
    return currentPlayer == PlayerType.player1
        ? player1DiceValue
        : player2DiceValue;
  }

  // 兼容性方法 - 检查是否已摇骰子
  bool get hasDiceRolled {
    return player1HasRolled && player2HasRolled;
  }

  // 兼容性方法 - 结束回合
  void endTurn() {
    skipMove();
  }

  bool get isGameOver {
    return player1Pieces.isEmpty || player2Pieces.isEmpty;
  }

  PlayerType? get winner {
    if (player1Pieces.isEmpty) return PlayerType.player2;
    if (player2Pieces.isEmpty) return PlayerType.player1;
    return null;
  }
}
