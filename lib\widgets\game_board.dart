import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/game_state.dart';
import '../models/piece.dart';

class GameBoard extends StatelessWidget {
  const GameBoard({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<GameState>(
      builder: (context, gameState, child) {
        return Container(
          padding: EdgeInsets.all(16),
          child: AspectRatio(
            aspectRatio: 1.0,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.brown[100],
                border: Border.all(color: Colors.brown, width: 2),
              ),
              child: GridView.builder(
                physics: NeverScrollableScrollPhysics(),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: GameState.boardSize,
                ),
                itemCount: GameState.boardSize * GameState.boardSize,
                itemBuilder: (context, index) {
                  int row = index ~/ GameState.boardSize;
                  int col = index % GameState.boardSize;
                  return _buildCell(context, gameState, row, col);
                },
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildCell(BuildContext context, GameState gameState, int row, int col) {
    GamePiece? piece = gameState.board[row][col];
    bool canMoveTo = gameState.selectedPiece != null && gameState.canMoveTo(row, col);
    
    return GestureDetector(
      onTap: () {
        if (piece != null) {
          gameState.selectPiece(piece);
        } else if (canMoveTo) {
          gameState.movePiece(row, col);
        }
      },
      child: Container(
        decoration: BoxDecoration(
          color: _getCellColor(row, col, canMoveTo),
          border: Border.all(
            color: Colors.brown[300]!,
            width: 0.5,
          ),
        ),
        child: piece != null ? _buildPiece(piece) : null,
      ),
    );
  }

  Color _getCellColor(int row, int col, bool canMoveTo) {
    if (canMoveTo) {
      return Colors.green[200]!;
    }
    return (row + col) % 2 == 0 ? Colors.brown[50]! : Colors.brown[100]!;
  }

  Widget _buildPiece(GamePiece piece) {
    return Container(
      margin: EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: piece.color,
        shape: BoxShape.circle,
        border: Border.all(
          color: piece.isSelected ? Colors.yellow : Colors.black,
          width: piece.isSelected ? 3 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black26,
            offset: Offset(1, 1),
            blurRadius: 2,
          ),
        ],
      ),
      child: Center(
        child: Text(
          piece.id.split('_')[1],
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 10,
          ),
        ),
      ),
    );
  }
}
