import 'package:flutter/material.dart';
import '../models/piece.dart';

class ReadyButtons extends StatelessWidget {
  final bool player1Ready;
  final bool player2Ready;
  final VoidCallback? onPlayer1Ready;
  final VoidCallback? onPlayer2Ready;
  final bool canReady;

  const ReadyButtons({
    Key? key,
    required this.player1Ready,
    required this.player2Ready,
    this.onPlayer1Ready,
    this.onPlayer2Ready,
    this.canReady = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        children: [
          Text(
            '准备阶段',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[700],
            ),
          ),
          Sized<PERSON><PERSON>(height: 16),
          Row(
            children: [
              // 玩家1就绪按钮
              Expanded(
                child: Column(
                  children: [
                    Text(
                      '玩家1',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.red,
                      ),
                    ),
                    SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: canReady && !player1Ready ? onPlayer1Ready : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: player1Ready ? Colors.green : Colors.red,
                        foregroundColor: Colors.white,
                        padding: EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          if (player1Ready) ...[
                            Icon(Icons.check, size: 20),
                            SizedBox(width: 4),
                          ],
                          Text(player1Ready ? '已就绪' : '就绪'),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              
              SizedBox(width: 16),
              
              // 玩家2就绪按钮
              Expanded(
                child: Column(
                  children: [
                    Text(
                      '玩家2',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                    ),
                    SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: canReady && !player2Ready ? onPlayer2Ready : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: player2Ready ? Colors.green : Colors.blue,
                        foregroundColor: Colors.white,
                        padding: EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          if (player2Ready) ...[
                            Icon(Icons.check, size: 20),
                            SizedBox(width: 4),
                          ],
                          Text(player2Ready ? '已就绪' : '就绪'),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          // 状态提示
          if (player1Ready && player2Ready) ...[
            SizedBox(height: 12),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.green[100],
                borderRadius: BorderRadius.circular(6),
              ),
              child: Text(
                '双方已就绪，正在摇骰子...',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.green[700],
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ] else ...[
            SizedBox(height: 12),
            Text(
              '等待双方玩家点击就绪按钮',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ],
        ],
      ),
    );
  }
}
