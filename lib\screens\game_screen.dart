import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/game_state.dart';
import '../models/piece.dart';
import '../widgets/game_board.dart';
import '../widgets/dice_widget.dart';

class GameScreen extends StatelessWidget {
  const GameScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('双人对战棋盘游戏'),
        backgroundColor: Colors.brown[300],
        elevation: 0,
      ),
      body: Consumer<GameState>(
        builder: (context, gameState, child) {
          return Column(
            children: [
              // 游戏状态信息
              _buildGameInfo(gameState),

              // 棋盘
              Expanded(child: GameBoard()),

              // 控制面板
              _buildControlPanel(context, gameState),
            ],
          );
        },
      ),
    );
  }

  Widget _buildGameInfo(GameState gameState) {
    return Container(
      padding: EdgeInsets.all(16),
      color: Colors.brown[50],
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          // 当前玩家
          Column(
            children: [
              Text(
                '当前玩家',
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
              ),
              Container(
                width: 30,
                height: 30,
                decoration: BoxDecoration(
                  color:
                      gameState.currentPlayer == PlayerType.player1
                          ? Colors.red
                          : Colors.blue,
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    gameState.currentPlayer == PlayerType.player1 ? '1' : '2',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),

          // 剩余棋子数
          Column(
            children: [
              Text('玩家1: ${gameState.player1Pieces.length}'),
              Text('玩家2: ${gameState.player2Pieces.length}'),
            ],
          ),

          // 游戏状态
          if (gameState.isGameOver)
            Column(
              children: [
                Text(
                  '游戏结束!',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                ),
                Text(
                  '玩家${gameState.winner == PlayerType.player1 ? '1' : '2'}获胜!',
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                ),
              ],
            ),
        ],
      ),
    );
  }

  Widget _buildControlPanel(BuildContext context, GameState gameState) {
    return Container(
      padding: EdgeInsets.all(16),
      color: Colors.brown[100],
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          // 骰子
          Column(
            children: [
              Text(
                '骰子',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              DiceWidget(
                value: gameState.diceValue,
                onRoll: gameState.rollDice,
                canRoll: !gameState.hasDiceRolled && !gameState.isGameOver,
              ),
            ],
          ),

          // 游戏说明
          Expanded(
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 16),
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.brown[300]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text('游戏说明:', style: TextStyle(fontWeight: FontWeight.bold)),
                  Text('1. 点击骰子摇出点数'),
                  Text('2. 选择己方棋子'),
                  Text('3. 点击可移动位置'),
                  Text('4. 移动距离=骰子点数'),
                  Text('5. 吃掉对方所有棋子获胜'),
                ],
              ),
            ),
          ),

          // 结束回合按钮
          Column(
            children: [
              ElevatedButton(
                onPressed:
                    gameState.hasMovedThisTurn && !gameState.isGameOver
                        ? gameState.endTurn
                        : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.brown[400],
                ),
                child: Text('结束回合'),
              ),
              SizedBox(height: 8),
              ElevatedButton(
                onPressed: () {
                  _showRestartDialog(context);
                },
                style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
                child: Text('重新开始'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showRestartDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('重新开始游戏'),
          content: Text('确定要重新开始游戏吗？'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('取消'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                Provider.of<GameState>(context, listen: false).initializeGame();
              },
              child: Text('确定'),
            ),
          ],
        );
      },
    );
  }
}
