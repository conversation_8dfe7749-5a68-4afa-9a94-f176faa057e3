import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/game_state.dart';
import '../models/piece.dart';
import '../widgets/game_board.dart';
import '../widgets/dual_dice_widget.dart';
import '../widgets/ready_buttons.dart';

class GameScreen extends StatelessWidget {
  const GameScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('双人对战棋盘游戏'),
        backgroundColor: Colors.brown[300],
        elevation: 0,
      ),
      body: Consumer<GameState>(
        builder: (context, gameState, child) {
          return Column(
            children: [
              // 游戏状态信息
              _buildGameInfo(gameState),

              // 棋盘
              Expanded(child: GameBoard()),

              // 控制面板
              _buildControlPanel(context, gameState),
            ],
          );
        },
      ),
    );
  }

  Widget _buildGameInfo(GameState gameState) {
    return Container(
      padding: EdgeInsets.all(16),
      color: Colors.brown[50],
      child: Column(
        children: [
          // 游戏阶段和状态信息
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              // 游戏阶段
              Column(
                children: [
                  Text(
                    '游戏阶段',
                    style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                  ),
                  Text(
                    gameState.phaseDescription,
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                ],
              ),

              // 当前玩家
              if (gameState.currentPhase == GamePhase.moving)
                Column(
                  children: [
                    Text(
                      '当前玩家',
                      style:
                          TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                    ),
                    Container(
                      width: 30,
                      height: 30,
                      decoration: BoxDecoration(
                        color: gameState.currentPlayer == PlayerType.player1
                            ? Colors.red
                            : Colors.blue,
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: Text(
                          gameState.currentPlayer == PlayerType.player1
                              ? '1'
                              : '2',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),

              // 剩余棋子数
              Column(
                children: [
                  Text(
                    '剩余棋子',
                    style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                  ),
                  Text('玩家1: ${gameState.player1Pieces.length}'),
                  Text('玩家2: ${gameState.player2Pieces.length}'),
                ],
              ),
            ],
          ),

          // 游戏结束状态
          if (gameState.isGameOver) ...[
            SizedBox(height: 12),
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red[300]!),
              ),
              child: Column(
                children: [
                  Text(
                    '游戏结束!',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                    ),
                  ),
                  Text(
                    '玩家${gameState.winner == PlayerType.player1 ? '1' : '2'}获胜!',
                    style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildControlPanel(BuildContext context, GameState gameState) {
    return Container(
      padding: EdgeInsets.all(16),
      color: Colors.brown[100],
      child: Column(
        children: [
          // 根据游戏阶段显示不同的控制面板
          if (gameState.currentPhase == GamePhase.preparation) ...[
            // 准备阶段 - 显示就绪按钮
            ReadyButtons(
              player1Ready: gameState.player1Ready,
              player2Ready: gameState.player2Ready,
              onPlayer1Ready: () =>
                  gameState.setPlayerReady(PlayerType.player1),
              onPlayer2Ready: () =>
                  gameState.setPlayerReady(PlayerType.player2),
              canReady: !gameState.isGameOver,
            ),
          ] else if (gameState.currentPhase == GamePhase.rolling) ...[
            // 摇骰子阶段 - 显示摇骰子动画或结果
            Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.amber[100],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.amber[300]!),
              ),
              child: Column(
                children: [
                  Text(
                    '正在摇骰子...',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.amber[800],
                    ),
                  ),
                  SizedBox(height: 8),
                  CircularProgressIndicator(
                    valueColor:
                        AlwaysStoppedAnimation<Color>(Colors.amber[600]!),
                  ),
                ],
              ),
            ),
          ] else if (gameState.currentPhase == GamePhase.moving) ...[
            // 移动阶段 - 显示双骰子和控制按钮
            Column(
              children: [
                // 双骰子显示
                DualDiceWidget(
                  player1Value: gameState.player1DiceValue,
                  player2Value: gameState.player2DiceValue,
                  comparisonResult: gameState.diceComparisonResult,
                  showResult: true,
                ),

                SizedBox(height: 16),

                // 控制按钮和说明
                Row(
                  children: [
                    // 游戏说明
                    Expanded(
                      child: Container(
                        padding: EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.brown[300]!),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text('游戏说明:',
                                style: TextStyle(fontWeight: FontWeight.bold)),
                            Text('1. 双方同时摇骰子'),
                            Text('2. 点数大者可移动棋子'),
                            Text('3. 平局时双方都可移动'),
                            Text('4. 移动距离=骰子点数'),
                            Text('5. 吃掉对方所有棋子获胜'),
                          ],
                        ),
                      ),
                    ),

                    SizedBox(width: 16),

                    // 控制按钮
                    Column(
                      children: [
                        // 跳过移动按钮
                        if (gameState.canPlayerMove(gameState.currentPlayer))
                          ElevatedButton(
                            onPressed: gameState.skipMove,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.orange[400],
                            ),
                            child: Text('跳过移动'),
                          ),

                        SizedBox(height: 8),

                        // 重新开始按钮
                        ElevatedButton(
                          onPressed: () {
                            _showRestartDialog(context);
                          },
                          style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.orange),
                          child: Text('重新开始'),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  void _showRestartDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('重新开始游戏'),
          content: Text('确定要重新开始游戏吗？'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('取消'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                Provider.of<GameState>(context, listen: false).initializeGame();
              },
              child: Text('确定'),
            ),
          ],
        );
      },
    );
  }
}
