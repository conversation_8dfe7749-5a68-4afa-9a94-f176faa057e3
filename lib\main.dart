import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'models/network_game_state.dart';
import 'screens/lobby_screen.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) {
        final gameState = NetworkGameState();
        // 初始化网络服务
        gameState.initializeNetworkService();
        return gameState;
      },
      child: MaterialApp(
        title: '双人对战棋盘游戏',
        theme: ThemeData(
          primarySwatch: Colors.brown,
          visualDensity: VisualDensity.adaptivePlatformDensity,
        ),
        home: LobbyScreen(),
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}
