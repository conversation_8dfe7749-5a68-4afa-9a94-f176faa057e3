import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/io.dart';
import 'package:uuid/uuid.dart';
import '../models/piece.dart';

// 网络消息类型
enum MessageType {
  joinRoom,
  leaveRoom,
  playerReady,
  diceRolled,
  pieceMove,
  gameState,
  playerJoined,
  playerLeft,
  error,
  roomCreated,
  roomList,
}

// 网络消息
class NetworkMessage {
  final MessageType type;
  final String roomId;
  final String playerId;
  final Map<String, dynamic> data;
  final DateTime timestamp;

  NetworkMessage({
    required this.type,
    required this.roomId,
    required this.playerId,
    required this.data,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  Map<String, dynamic> toJson() {
    return {
      'type': type.toString(),
      'roomId': roomId,
      'playerId': playerId,
      'data': data,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  factory NetworkMessage.fromJson(Map<String, dynamic> json) {
    return NetworkMessage(
      type: MessageType.values.firstWhere(
        (e) => e.toString() == json['type'],
        orElse: () => MessageType.error,
      ),
      roomId: json['roomId'] ?? '',
      playerId: json['playerId'] ?? '',
      data: json['data'] ?? {},
      timestamp: DateTime.parse(json['timestamp']),
    );
  }
}

// 房间信息
class RoomInfo {
  final String id;
  final String name;
  final List<String> players;
  final bool isGameStarted;
  final DateTime createdAt;

  RoomInfo({
    required this.id,
    required this.name,
    required this.players,
    required this.isGameStarted,
    required this.createdAt,
  });

  factory RoomInfo.fromJson(Map<String, dynamic> json) {
    return RoomInfo(
      id: json['id'],
      name: json['name'],
      players: List<String>.from(json['players']),
      isGameStarted: json['isGameStarted'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'players': players,
      'isGameStarted': isGameStarted,
      'createdAt': createdAt.toIso8601String(),
    };
  }
}

// 网络服务类
class NetworkService {
  static final NetworkService _instance = NetworkService._internal();
  factory NetworkService() => _instance;
  NetworkService._internal();

  WebSocketChannel? _channel;
  final StreamController<NetworkMessage> _messageController = StreamController.broadcast();
  final StreamController<bool> _connectionController = StreamController.broadcast();
  
  String? _currentRoomId;
  String? _playerId;
  bool _isConnected = false;
  Timer? _heartbeatTimer;
  Timer? _reconnectTimer;
  
  // 简单的本地WebSocket服务器地址（用于测试）
  static const String _serverUrl = 'ws://localhost:8080';
  
  Stream<NetworkMessage> get messageStream => _messageController.stream;
  Stream<bool> get connectionStream => _connectionController.stream;
  
  bool get isConnected => _isConnected;
  String? get currentRoomId => _currentRoomId;
  String? get playerId => _playerId;

  // 初始化网络服务
  Future<void> initialize() async {
    _playerId = const Uuid().v4();
    await _connect();
  }

  // 连接到服务器
  Future<void> _connect() async {
    try {
      // 注意：这里使用的是一个简化的WebSocket服务器
      // 在实际部署中，您需要部署一个真正的WebSocket服务器
      _channel = IOWebSocketChannel.connect(_serverUrl);
      
      _isConnected = true;
      _connectionController.add(true);
      
      // 监听消息
      _channel!.stream.listen(
        _onMessage,
        onError: _onError,
        onDone: _onDisconnected,
      );
      
      // 启动心跳
      _startHeartbeat();
      
    } catch (e) {
      print('连接失败: $e');
      _isConnected = false;
      _connectionController.add(false);
      _scheduleReconnect();
    }
  }

  // 处理接收到的消息
  void _onMessage(dynamic message) {
    try {
      final data = jsonDecode(message);
      final networkMessage = NetworkMessage.fromJson(data);
      _messageController.add(networkMessage);
    } catch (e) {
      print('解析消息失败: $e');
    }
  }

  // 处理连接错误
  void _onError(error) {
    print('WebSocket错误: $error');
    _isConnected = false;
    _connectionController.add(false);
    _scheduleReconnect();
  }

  // 处理连接断开
  void _onDisconnected() {
    print('WebSocket连接断开');
    _isConnected = false;
    _connectionController.add(false);
    _heartbeatTimer?.cancel();
    _scheduleReconnect();
  }

  // 发送消息
  void _sendMessage(NetworkMessage message) {
    if (_isConnected && _channel != null) {
      try {
        _channel!.sink.add(jsonEncode(message.toJson()));
      } catch (e) {
        print('发送消息失败: $e');
      }
    }
  }

  // 启动心跳
  void _startHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = Timer.periodic(Duration(seconds: 30), (timer) {
      if (_isConnected) {
        _sendMessage(NetworkMessage(
          type: MessageType.gameState,
          roomId: _currentRoomId ?? '',
          playerId: _playerId ?? '',
          data: {'heartbeat': true},
        ));
      }
    });
  }

  // 安排重连
  void _scheduleReconnect() {
    _reconnectTimer?.cancel();
    _reconnectTimer = Timer(Duration(seconds: 5), () {
      if (!_isConnected) {
        print('尝试重新连接...');
        _connect();
      }
    });
  }

  // 创建房间
  Future<void> createRoom(String roomName) async {
    _sendMessage(NetworkMessage(
      type: MessageType.joinRoom,
      roomId: const Uuid().v4(),
      playerId: _playerId!,
      data: {
        'action': 'create',
        'roomName': roomName,
      },
    ));
  }

  // 加入房间
  Future<void> joinRoom(String roomId) async {
    _currentRoomId = roomId;
    _sendMessage(NetworkMessage(
      type: MessageType.joinRoom,
      roomId: roomId,
      playerId: _playerId!,
      data: {'action': 'join'},
    ));
  }

  // 离开房间
  Future<void> leaveRoom() async {
    if (_currentRoomId != null) {
      _sendMessage(NetworkMessage(
        type: MessageType.leaveRoom,
        roomId: _currentRoomId!,
        playerId: _playerId!,
        data: {},
      ));
      _currentRoomId = null;
    }
  }

  // 发送玩家就绪状态
  void sendPlayerReady(PlayerType player) {
    if (_currentRoomId != null) {
      _sendMessage(NetworkMessage(
        type: MessageType.playerReady,
        roomId: _currentRoomId!,
        playerId: _playerId!,
        data: {
          'player': player.toString(),
          'ready': true,
        },
      ));
    }
  }

  // 发送骰子结果
  void sendDiceResult(int player1Dice, int player2Dice) {
    if (_currentRoomId != null) {
      _sendMessage(NetworkMessage(
        type: MessageType.diceRolled,
        roomId: _currentRoomId!,
        playerId: _playerId!,
        data: {
          'player1Dice': player1Dice,
          'player2Dice': player2Dice,
        },
      ));
    }
  }

  // 发送棋子移动
  void sendPieceMove(String pieceId, int fromRow, int fromCol, int toRow, int toCol) {
    if (_currentRoomId != null) {
      _sendMessage(NetworkMessage(
        type: MessageType.pieceMove,
        roomId: _currentRoomId!,
        playerId: _playerId!,
        data: {
          'pieceId': pieceId,
          'fromRow': fromRow,
          'fromCol': fromCol,
          'toRow': toRow,
          'toCol': toCol,
        },
      ));
    }
  }

  // 断开连接
  void disconnect() {
    _heartbeatTimer?.cancel();
    _reconnectTimer?.cancel();
    _channel?.sink.close();
    _isConnected = false;
    _connectionController.add(false);
  }

  // 清理资源
  void dispose() {
    disconnect();
    _messageController.close();
    _connectionController.close();
  }
}
