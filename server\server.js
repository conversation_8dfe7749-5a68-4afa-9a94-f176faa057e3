const WebSocket = require('ws');
const { v4: uuidv4 } = require('uuid');

// 创建WebSocket服务器
const wss = new WebSocket.Server({ 
  port: 8080,
  host: '0.0.0.0' // 允许外部连接
});

// 存储房间和玩家信息
const rooms = new Map();
const players = new Map();

// 消息类型
const MessageType = {
  JOIN_ROOM: 'MessageType.joinRoom',
  LEAVE_ROOM: 'MessageType.leaveRoom',
  PLAYER_READY: 'MessageType.playerReady',
  DICE_ROLLED: 'MessageType.diceRolled',
  PIECE_MOVE: 'MessageType.pieceMove',
  GAME_STATE: 'MessageType.gameState',
  PLAYER_JOINED: 'MessageType.playerJoined',
  PLAYER_LEFT: 'MessageType.playerLeft',
  ERROR: 'MessageType.error',
  ROOM_CREATED: 'MessageType.roomCreated',
  ROOM_LIST: 'MessageType.roomList'
};

// 房间类
class Room {
  constructor(id, name, creator) {
    this.id = id;
    this.name = name;
    this.players = new Map();
    this.gameState = {
      phase: 'preparation',
      player1Ready: false,
      player2Ready: false,
      player1Dice: 1,
      player2Dice: 1,
      currentPlayer: 'player1',
      playersCanMove: [],
      board: null
    };
    this.createdAt = new Date();
    this.creator = creator;
  }

  addPlayer(playerId, ws) {
    if (this.players.size >= 2) {
      return false; // 房间已满
    }
    
    const playerType = this.players.size === 0 ? 'player1' : 'player2';
    this.players.set(playerId, {
      ws: ws,
      type: playerType,
      ready: false
    });
    
    return true;
  }

  removePlayer(playerId) {
    this.players.delete(playerId);
  }

  broadcast(message, excludePlayerId = null) {
    this.players.forEach((player, id) => {
      if (id !== excludePlayerId && player.ws.readyState === WebSocket.OPEN) {
        player.ws.send(JSON.stringify(message));
      }
    });
  }

  getPlayerType(playerId) {
    const player = this.players.get(playerId);
    return player ? player.type : null;
  }
}

// 处理WebSocket连接
wss.on('connection', (ws) => {
  console.log('新的客户端连接');
  
  let playerId = null;
  let currentRoomId = null;

  ws.on('message', (data) => {
    try {
      const message = JSON.parse(data);
      console.log('收到消息:', message);

      switch (message.type) {
        case MessageType.JOIN_ROOM:
          handleJoinRoom(message);
          break;
        case MessageType.LEAVE_ROOM:
          handleLeaveRoom(message);
          break;
        case MessageType.PLAYER_READY:
          handlePlayerReady(message);
          break;
        case MessageType.DICE_ROLLED:
          handleDiceRolled(message);
          break;
        case MessageType.PIECE_MOVE:
          handlePieceMove(message);
          break;
        case MessageType.GAME_STATE:
          handleGameState(message);
          break;
        default:
          console.log('未知消息类型:', message.type);
      }
    } catch (error) {
      console.error('处理消息错误:', error);
      sendError(ws, '消息格式错误');
    }
  });

  ws.on('close', () => {
    console.log('客户端断开连接');
    if (playerId && currentRoomId) {
      handlePlayerDisconnect(playerId, currentRoomId);
    }
  });

  // 处理加入房间
  function handleJoinRoom(message) {
    playerId = message.playerId;
    const { action, roomName } = message.data;

    if (action === 'create') {
      // 创建新房间
      const roomId = message.roomId;
      const room = new Room(roomId, roomName, playerId);
      
      if (room.addPlayer(playerId, ws)) {
        rooms.set(roomId, room);
        players.set(playerId, { roomId, ws });
        currentRoomId = roomId;

        // 发送房间创建成功消息
        ws.send(JSON.stringify({
          type: MessageType.ROOM_CREATED,
          roomId: roomId,
          playerId: playerId,
          data: {
            roomId: roomId,
            roomName: roomName,
            playerType: 'player1'
          }
        }));

        console.log(`房间 ${roomId} 创建成功，创建者: ${playerId}`);
      }
    } else if (action === 'join') {
      // 加入现有房间
      const roomId = message.roomId;
      const room = rooms.get(roomId);

      if (!room) {
        sendError(ws, '房间不存在');
        return;
      }

      if (room.addPlayer(playerId, ws)) {
        players.set(playerId, { roomId, ws });
        currentRoomId = roomId;

        const playerType = room.getPlayerType(playerId);

        // 通知新玩家加入成功
        ws.send(JSON.stringify({
          type: MessageType.PLAYER_JOINED,
          roomId: roomId,
          playerId: playerId,
          data: {
            playerType: playerType,
            gameState: room.gameState
          }
        }));

        // 通知房间内其他玩家
        room.broadcast({
          type: MessageType.PLAYER_JOINED,
          roomId: roomId,
          playerId: playerId,
          data: {
            newPlayer: playerId,
            playerType: playerType
          }
        }, playerId);

        console.log(`玩家 ${playerId} 加入房间 ${roomId}`);
      } else {
        sendError(ws, '房间已满');
      }
    }
  }

  // 处理离开房间
  function handleLeaveRoom(message) {
    const roomId = message.roomId;
    const room = rooms.get(roomId);

    if (room) {
      room.removePlayer(playerId);
      
      // 通知其他玩家
      room.broadcast({
        type: MessageType.PLAYER_LEFT,
        roomId: roomId,
        playerId: playerId,
        data: { leftPlayer: playerId }
      });

      // 如果房间为空，删除房间
      if (room.players.size === 0) {
        rooms.delete(roomId);
        console.log(`房间 ${roomId} 已删除`);
      }
    }

    players.delete(playerId);
    currentRoomId = null;
  }

  // 处理玩家就绪
  function handlePlayerReady(message) {
    const roomId = message.roomId;
    const room = rooms.get(roomId);

    if (room) {
      const playerType = room.getPlayerType(playerId);
      
      if (playerType === 'player1') {
        room.gameState.player1Ready = true;
      } else if (playerType === 'player2') {
        room.gameState.player2Ready = true;
      }

      // 广播玩家就绪状态
      room.broadcast({
        type: MessageType.PLAYER_READY,
        roomId: roomId,
        playerId: playerId,
        data: {
          playerType: playerType,
          ready: true,
          gameState: room.gameState
        }
      });

      console.log(`玩家 ${playerId} (${playerType}) 已就绪`);
    }
  }

  // 处理骰子结果
  function handleDiceRolled(message) {
    const roomId = message.roomId;
    const room = rooms.get(roomId);

    if (room) {
      room.gameState.player1Dice = message.data.player1Dice;
      room.gameState.player2Dice = message.data.player2Dice;
      room.gameState.phase = 'moving';

      // 确定谁可以移动
      if (room.gameState.player1Dice > room.gameState.player2Dice) {
        room.gameState.playersCanMove = ['player1'];
        room.gameState.currentPlayer = 'player1';
      } else if (room.gameState.player2Dice > room.gameState.player1Dice) {
        room.gameState.playersCanMove = ['player2'];
        room.gameState.currentPlayer = 'player2';
      } else {
        room.gameState.playersCanMove = ['player1', 'player2'];
        room.gameState.currentPlayer = 'player1';
      }

      // 广播骰子结果
      room.broadcast({
        type: MessageType.DICE_ROLLED,
        roomId: roomId,
        playerId: playerId,
        data: {
          player1Dice: room.gameState.player1Dice,
          player2Dice: room.gameState.player2Dice,
          gameState: room.gameState
        }
      });

      console.log(`房间 ${roomId} 骰子结果: P1=${room.gameState.player1Dice}, P2=${room.gameState.player2Dice}`);
    }
  }

  // 处理棋子移动
  function handlePieceMove(message) {
    const roomId = message.roomId;
    const room = rooms.get(roomId);

    if (room) {
      // 广播棋子移动
      room.broadcast({
        type: MessageType.PIECE_MOVE,
        roomId: roomId,
        playerId: playerId,
        data: message.data
      });

      console.log(`房间 ${roomId} 棋子移动:`, message.data);
    }
  }

  // 处理游戏状态（心跳等）
  function handleGameState(message) {
    if (message.data.heartbeat) {
      // 心跳响应
      ws.send(JSON.stringify({
        type: MessageType.GAME_STATE,
        roomId: message.roomId,
        playerId: message.playerId,
        data: { heartbeat: 'pong' }
      }));
    }
  }

  // 处理玩家断开连接
  function handlePlayerDisconnect(playerId, roomId) {
    const room = rooms.get(roomId);
    if (room) {
      room.removePlayer(playerId);
      
      // 通知其他玩家
      room.broadcast({
        type: MessageType.PLAYER_LEFT,
        roomId: roomId,
        playerId: playerId,
        data: { leftPlayer: playerId, reason: 'disconnect' }
      });

      // 如果房间为空，删除房间
      if (room.players.size === 0) {
        rooms.delete(roomId);
        console.log(`房间 ${roomId} 已删除（无玩家）`);
      }
    }
    players.delete(playerId);
  }

  // 发送错误消息
  function sendError(ws, errorMessage) {
    ws.send(JSON.stringify({
      type: MessageType.ERROR,
      roomId: '',
      playerId: '',
      data: { error: errorMessage }
    }));
  }
});

console.log('WebSocket服务器启动在端口 8080');
console.log('服务器时间:', new Date().toLocaleString());

// 定期清理空房间
setInterval(() => {
  rooms.forEach((room, roomId) => {
    if (room.players.size === 0) {
      rooms.delete(roomId);
      console.log(`清理空房间: ${roomId}`);
    }
  });
}, 60000); // 每分钟清理一次
