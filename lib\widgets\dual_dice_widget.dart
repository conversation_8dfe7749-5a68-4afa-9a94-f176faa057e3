import 'package:flutter/material.dart';
import 'dice_widget.dart';

class DualDiceWidget extends StatelessWidget {
  final int player1Value;
  final int player2Value;
  final String comparisonResult;
  final bool showResult;

  const DualDiceWidget({
    Key? key,
    required this.player1Value,
    required this.player2Value,
    required this.comparisonResult,
    this.showResult = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(
          '骰子对比',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            // 玩家1骰子
            Column(
              children: [
                Text(
                  '玩家1',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                ),
                SizedBox(height: 4),
                DiceWidget(
                  value: player1Value,
                  canRoll: false,
                ),
              ],
            ),
            
            // VS 标识
            Column(
              children: [
                Text(
                  'VS',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[600],
                  ),
                ),
                SizedBox(height: 20),
                Icon(
                  Icons.compare_arrows,
                  size: 30,
                  color: Colors.grey[600],
                ),
              ],
            ),
            
            // 玩家2骰子
            Column(
              children: [
                Text(
                  '玩家2',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue,
                  ),
                ),
                SizedBox(height: 4),
                DiceWidget(
                  value: player2Value,
                  canRoll: false,
                ),
              ],
            ),
          ],
        ),
        
        // 比较结果
        if (showResult && comparisonResult.isNotEmpty) ...[
          SizedBox(height: 12),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.amber[100],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.amber[300]!),
            ),
            child: Text(
              comparisonResult,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.amber[800],
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ],
    );
  }
}
