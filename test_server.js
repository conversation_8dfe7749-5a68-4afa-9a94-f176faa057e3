// 简单的WebSocket服务器测试脚本
// 用于在没有Node.js环境时进行本地测试

const WebSocket = require('ws');

// 创建一个简单的测试服务器
const wss = new WebSocket.Server({ port: 8080 });

console.log('测试WebSocket服务器启动在端口 8080');

wss.on('connection', function connection(ws) {
  console.log('新的客户端连接');

  // 发送欢迎消息
  ws.send(JSON.stringify({
    type: 'MessageType.gameState',
    roomId: '',
    playerId: '',
    data: { message: '连接成功' }
  }));

  ws.on('message', function incoming(data) {
    console.log('收到消息:', data.toString());
    
    try {
      const message = JSON.parse(data);
      
      // 简单的回显测试
      ws.send(JSON.stringify({
        type: 'MessageType.gameState',
        roomId: message.roomId || '',
        playerId: message.playerId || '',
        data: { echo: message, timestamp: new Date().toISOString() }
      }));
      
    } catch (e) {
      console.error('解析消息错误:', e);
    }
  });

  ws.on('close', function close() {
    console.log('客户端断开连接');
  });
});

// 优雅关闭
process.on('SIGINT', function() {
  console.log('\n正在关闭服务器...');
  wss.close(function() {
    console.log('服务器已关闭');
    process.exit(0);
  });
});
