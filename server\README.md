# 双人棋盘游戏 WebSocket 服务器部署指南

## 服务器要求
- Linux 服务器（已安装宝塔面板）
- Node.js 14.0.0 或更高版本
- 开放端口 8080（或其他您选择的端口）

## 部署步骤

### 1. 在宝塔面板中安装 Node.js
1. 登录宝塔面板
2. 进入"软件商店"
3. 搜索并安装"Node.js版本管理器"
4. 安装 Node.js 16.x 或更高版本

### 2. 上传服务器文件
1. 在宝塔面板中创建一个新的网站目录，例如：`/www/wwwroot/chess-game-server`
2. 将 `server` 文件夹中的所有文件上传到该目录
3. 确保文件结构如下：
   ```
   /www/wwwroot/chess-game-server/
   ├── server.js
   ├── package.json
   └── README.md
   ```

### 3. 安装依赖
1. 在宝塔面板中打开"终端"
2. 切换到服务器目录：
   ```bash
   cd /www/wwwroot/chess-game-server
   ```
3. 安装依赖：
   ```bash
   npm install
   ```

### 4. 配置防火墙
1. 在宝塔面板中进入"安全"设置
2. 添加端口规则，开放端口 8080
3. 确保服务器提供商的安全组也开放了该端口

### 5. 启动服务器
有两种方式启动服务器：

#### 方式一：直接启动（测试用）
```bash
cd /www/wwwroot/chess-game-server
node server.js
```

#### 方式二：使用 PM2 管理（推荐生产环境）
1. 安装 PM2：
   ```bash
   npm install -g pm2
   ```
2. 启动服务：
   ```bash
   pm2 start server.js --name chess-game-server
   ```
3. 设置开机自启：
   ```bash
   pm2 startup
   pm2 save
   ```

### 6. 验证服务器运行
1. 检查服务器是否正在运行：
   ```bash
   pm2 status
   ```
2. 查看日志：
   ```bash
   pm2 logs chess-game-server
   ```

### 7. 配置客户端连接
1. 获取您的服务器IP地址
2. 在客户端代码中修改 `lib/services/network_service.dart` 文件：
   ```dart
   // 将 localhost 替换为您的服务器IP
   static const String _serverUrl = 'ws://您的服务器IP:8080';
   ```

## 常用 PM2 命令
- 启动服务：`pm2 start server.js --name chess-game-server`
- 停止服务：`pm2 stop chess-game-server`
- 重启服务：`pm2 restart chess-game-server`
- 删除服务：`pm2 delete chess-game-server`
- 查看状态：`pm2 status`
- 查看日志：`pm2 logs chess-game-server`
- 监控：`pm2 monit`

## 故障排除

### 连接失败
1. 检查防火墙设置
2. 确认端口是否开放
3. 检查服务器是否正在运行
4. 验证IP地址是否正确

### 服务器崩溃
1. 查看 PM2 日志：`pm2 logs chess-game-server`
2. 检查 Node.js 版本是否兼容
3. 确认依赖是否正确安装

### 性能优化
1. 使用 PM2 集群模式：
   ```bash
   pm2 start server.js --name chess-game-server -i max
   ```
2. 配置 Nginx 反向代理（可选）

## 安全建议
1. 定期更新 Node.js 和依赖包
2. 配置适当的防火墙规则
3. 考虑使用 HTTPS/WSS（WebSocket Secure）
4. 实施速率限制和连接数限制

## 监控和日志
- 服务器日志位置：PM2 自动管理
- 查看实时日志：`pm2 logs chess-game-server --lines 100`
- 监控服务器状态：`pm2 monit`

## 扩展功能
如需添加更多功能，可以考虑：
1. 数据库集成（存储游戏记录）
2. 用户认证系统
3. 游戏回放功能
4. 排行榜系统
5. 聊天功能

## 联系支持
如果在部署过程中遇到问题，请检查：
1. Node.js 版本是否正确
2. 端口是否被占用
3. 防火墙配置是否正确
4. 网络连接是否正常
