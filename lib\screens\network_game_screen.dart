import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/network_game_state.dart';
import '../models/piece.dart';
import '../widgets/game_board.dart';
import '../widgets/dual_dice_widget.dart';
import '../widgets/ready_buttons.dart';

class NetworkGameScreen extends StatelessWidget {
  final bool isHost;
  
  const NetworkGameScreen({Key? key, required this.isHost}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('联机对战'),
        backgroundColor: Colors.brown[300],
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(Icons.exit_to_app),
            onPressed: () => _showLeaveDialog(context),
          ),
        ],
      ),
      body: Consumer<NetworkGameState>(
        builder: (context, gameState, child) {
          return Column(
            children: [
              // 网络状态和房间信息
              _buildNetworkInfo(gameState),

              // 游戏状态信息
              _buildGameInfo(gameState),

              // 棋盘
              Expanded(child: GameBoard()),

              // 控制面板
              _buildControlPanel(context, gameState),
            ],
          );
        },
      ),
    );
  }

  Widget _buildNetworkInfo(NetworkGameState gameState) {
    return Container(
      padding: EdgeInsets.all(12),
      color: Colors.blue[50],
      child: Row(
        children: [
          // 连接状态
          Container(
            padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: gameState.isConnected ? Colors.green[100] : Colors.red[100],
              borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  gameState.isConnected ? Icons.wifi : Icons.wifi_off,
                  size: 16,
                  color: gameState.isConnected ? Colors.green[700] : Colors.red[700],
                ),
                SizedBox(width: 4),
                Text(
                  gameState.isConnected ? '已连接' : '连接断开',
                  style: TextStyle(
                    fontSize: 12,
                    color: gameState.isConnected ? Colors.green[700] : Colors.red[700],
                  ),
                ),
              ],
            ),
          ),
          
          SizedBox(width: 12),
          
          // 房间信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '房间ID: ${gameState.roomId ?? "未知"}',
                  style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
                ),
                Text(
                  '角色: ${gameState.localPlayerType == PlayerType.player1 ? "玩家1" : "玩家2"} ${isHost ? "(房主)" : "(客人)"}',
                  style: TextStyle(fontSize: 11, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          
          // 复制房间ID按钮
          if (isHost)
            IconButton(
              icon: Icon(Icons.copy, size: 20),
              onPressed: () => _copyRoomId(context, gameState.roomId),
              tooltip: '复制房间ID',
            ),
        ],
      ),
    );
  }

  Widget _buildGameInfo(NetworkGameState gameState) {
    return Container(
      padding: EdgeInsets.all(16),
      color: Colors.brown[50],
      child: Column(
        children: [
          // 游戏阶段和状态信息
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              // 游戏阶段
              Column(
                children: [
                  Text(
                    '游戏阶段',
                    style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                  ),
                  Text(
                    gameState.phaseDescription,
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                ],
              ),

              // 当前玩家
              if (gameState.currentPhase == GamePhase.moving)
                Column(
                  children: [
                    Text(
                      '当前玩家',
                      style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                    ),
                    Container(
                      width: 30,
                      height: 30,
                      decoration: BoxDecoration(
                        color: gameState.currentPlayer == PlayerType.player1
                            ? Colors.red
                            : Colors.blue,
                        shape: BoxShape.circle,
                        border: gameState.isLocalPlayerTurn 
                            ? Border.all(color: Colors.yellow, width: 3)
                            : null,
                      ),
                      child: Center(
                        child: Text(
                          gameState.currentPlayer == PlayerType.player1 ? '1' : '2',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    if (gameState.isLocalPlayerTurn)
                      Text(
                        '你的回合',
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.orange[700],
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                  ],
                ),

              // 剩余棋子数
              Column(
                children: [
                  Text(
                    '剩余棋子',
                    style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                  ),
                  Text('玩家1: ${gameState.player1Pieces.length}'),
                  Text('玩家2: ${gameState.player2Pieces.length}'),
                ],
              ),
            ],
          ),

          // 游戏结束状态
          if (gameState.isGameOver) ...[
            SizedBox(height: 12),
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red[300]!),
              ),
              child: Column(
                children: [
                  Text(
                    '游戏结束!',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                    ),
                  ),
                  Text(
                    '玩家${gameState.winner == PlayerType.player1 ? '1' : '2'}获胜!',
                    style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                  ),
                  if (gameState.winner == gameState.localPlayerType)
                    Text(
                      '恭喜你获胜！',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.green[700],
                        fontWeight: FontWeight.bold,
                      ),
                    )
                  else
                    Text(
                      '很遗憾，你败了',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.red[700],
                      ),
                    ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildControlPanel(BuildContext context, NetworkGameState gameState) {
    return Container(
      padding: EdgeInsets.all(16),
      color: Colors.brown[100],
      child: Column(
        children: [
          // 根据游戏阶段显示不同的控制面板
          if (gameState.currentPhase == GamePhase.preparation) ...[
            // 准备阶段 - 显示就绪按钮
            ReadyButtons(
              player1Ready: gameState.player1Ready,
              player2Ready: gameState.player2Ready,
              onPlayer1Ready: gameState.localPlayerType == PlayerType.player1 
                  ? () => gameState.setPlayerReady(PlayerType.player1)
                  : null,
              onPlayer2Ready: gameState.localPlayerType == PlayerType.player2 
                  ? () => gameState.setPlayerReady(PlayerType.player2)
                  : null,
              canReady: !gameState.isGameOver,
            ),
          ] else if (gameState.currentPhase == GamePhase.rolling) ...[
            // 摇骰子阶段
            Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.amber[100],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.amber[300]!),
              ),
              child: Column(
                children: [
                  Text(
                    isHost ? '正在摇骰子...' : '房主正在摇骰子...',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.amber[800],
                    ),
                  ),
                  SizedBox(height: 8),
                  CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.amber[600]!),
                  ),
                ],
              ),
            ),
          ] else if (gameState.currentPhase == GamePhase.moving) ...[
            // 移动阶段
            Column(
              children: [
                // 双骰子显示
                DualDiceWidget(
                  player1Value: gameState.player1DiceValue,
                  player2Value: gameState.player2DiceValue,
                  comparisonResult: gameState.diceComparisonResult,
                  showResult: true,
                ),
                
                SizedBox(height: 16),
                
                // 控制按钮和说明
                Row(
                  children: [
                    // 游戏说明
                    Expanded(
                      child: Container(
                        padding: EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.brown[300]!),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text('联机游戏说明:', style: TextStyle(fontWeight: FontWeight.bold)),
                            Text('1. 双方同时摇骰子'),
                            Text('2. 点数大者可移动棋子'),
                            Text('3. 平局时双方都可移动'),
                            Text('4. 每次只能移动一格'),
                            Text('5. 吃掉对方所有棋子获胜'),
                          ],
                        ),
                      ),
                    ),
                    
                    SizedBox(width: 16),
                    
                    // 控制按钮
                    Column(
                      children: [
                        // 跳过移动按钮
                        if (gameState.canPlayerMove(gameState.localPlayerType!) && 
                            gameState.isLocalPlayerTurn)
                          ElevatedButton(
                            onPressed: gameState.skipMove,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.orange[400],
                            ),
                            child: Text('跳过移动'),
                          ),
                        
                        SizedBox(height: 8),
                        
                        // 离开房间按钮
                        ElevatedButton(
                          onPressed: () => _showLeaveDialog(context),
                          style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                          child: Text('离开房间'),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  void _copyRoomId(BuildContext context, String? roomId) {
    if (roomId != null) {
      // 这里应该使用 Clipboard.setData，但为了简化，我们显示一个对话框
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text('房间ID'),
          content: SelectableText(roomId),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text('关闭'),
            ),
          ],
        ),
      );
    }
  }

  void _showLeaveDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('离开房间'),
          content: Text('确定要离开房间吗？'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('取消'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _leaveRoom(context);
              },
              child: Text('确定'),
            ),
          ],
        );
      },
    );
  }

  void _leaveRoom(BuildContext context) {
    final gameState = Provider.of<NetworkGameState>(context, listen: false);
    gameState.leaveRoom();
    Navigator.of(context).pop(); // 返回到大厅
  }
}
