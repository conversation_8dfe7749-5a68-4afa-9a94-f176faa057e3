import 'package:flutter/material.dart';

class DiceWidget extends StatelessWidget {
  final int value;
  final VoidCallback? onRoll;
  final bool canRoll;

  const DiceWidget({
    Key? key,
    required this.value,
    this.onRoll,
    this.canRoll = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: canRoll ? onRoll : null,
      child: Container(
        width: 80,
        height: 80,
        decoration: BoxDecoration(
          color: canRoll ? Colors.white : Colors.grey[300],
          border: Border.all(color: Colors.black, width: 2),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black26,
              offset: Offset(2, 2),
              blurRadius: 4,
            ),
          ],
        ),
        child: Center(
          child: _buildDots(),
        ),
      ),
    );
  }

  Widget _buildDots() {
    return CustomPaint(
      size: Size(60, 60),
      painter: <PERSON><PERSON><PERSON><PERSON><PERSON>(value),
    );
  }
}

class DicePainter extends CustomPainter {
  final int value;

  DicePainter(this.value);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.fill;

    final double dotRadius = size.width * 0.08;
    final double spacing = size.width * 0.25;

    switch (value) {
      case 1:
        _drawDot(canvas, paint, size.width / 2, size.height / 2, dotRadius);
        break;
      case 2:
        _drawDot(canvas, paint, spacing, spacing, dotRadius);
        _drawDot(canvas, paint, size.width - spacing, size.height - spacing, dotRadius);
        break;
      case 3:
        _drawDot(canvas, paint, spacing, spacing, dotRadius);
        _drawDot(canvas, paint, size.width / 2, size.height / 2, dotRadius);
        _drawDot(canvas, paint, size.width - spacing, size.height - spacing, dotRadius);
        break;
      case 4:
        _drawDot(canvas, paint, spacing, spacing, dotRadius);
        _drawDot(canvas, paint, size.width - spacing, spacing, dotRadius);
        _drawDot(canvas, paint, spacing, size.height - spacing, dotRadius);
        _drawDot(canvas, paint, size.width - spacing, size.height - spacing, dotRadius);
        break;
      case 5:
        _drawDot(canvas, paint, spacing, spacing, dotRadius);
        _drawDot(canvas, paint, size.width - spacing, spacing, dotRadius);
        _drawDot(canvas, paint, size.width / 2, size.height / 2, dotRadius);
        _drawDot(canvas, paint, spacing, size.height - spacing, dotRadius);
        _drawDot(canvas, paint, size.width - spacing, size.height - spacing, dotRadius);
        break;
      case 6:
        _drawDot(canvas, paint, spacing, spacing, dotRadius);
        _drawDot(canvas, paint, size.width - spacing, spacing, dotRadius);
        _drawDot(canvas, paint, spacing, size.height / 2, dotRadius);
        _drawDot(canvas, paint, size.width - spacing, size.height / 2, dotRadius);
        _drawDot(canvas, paint, spacing, size.height - spacing, dotRadius);
        _drawDot(canvas, paint, size.width - spacing, size.height - spacing, dotRadius);
        break;
    }
  }

  void _drawDot(Canvas canvas, Paint paint, double x, double y, double radius) {
    canvas.drawCircle(Offset(x, y), radius, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is DicePainter && oldDelegate.value != value;
  }
}
