import 'package:flutter/material.dart';

enum PlayerType { player1, player2 }

class Game<PERSON>iece {
  final String id;
  final PlayerType player;
  int row;
  int col;
  bool isSelected;

  GamePiece({
    required this.id,
    required this.player,
    required this.row,
    required this.col,
    this.isSelected = false,
  });

  Color get color {
    return player == PlayerType.player1 ? Colors.red : Colors.blue;
  }

  GamePiece copyWith({
    String? id,
    PlayerType? player,
    int? row,
    int? col,
    bool? isSelected,
  }) {
    return GamePiece(
      id: id ?? this.id,
      player: player ?? this.player,
      row: row ?? this.row,
      col: col ?? this.col,
      isSelected: isSelected ?? this.isSelected,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is GamePiece && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
